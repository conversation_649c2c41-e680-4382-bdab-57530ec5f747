{"name": "gmail-cli", "version": "1.0.0", "description": "A command-line interface for Gmail using TypeScript", "main": "dist/index.js", "bin": {"gmail-cli": "dist/index.js"}, "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["gmail", "cli", "typescript", "email", "google-api"], "author": "", "license": "MIT", "dependencies": {"googleapis": "^128.0.0", "commander": "^11.1.0", "inquirer": "^9.2.12", "dotenv": "^16.3.1", "chalk": "^5.3.0", "cli-table3": "^0.6.3", "csv-writer": "^1.6.0", "open": "^9.1.0"}, "devDependencies": {"@types/node": "^20.8.0", "@types/inquirer": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "eslint": "^8.50.0", "jest": "^29.7.0", "@types/jest": "^29.5.5", "prettier": "^3.0.3", "rimraf": "^5.0.5", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=16.0.0"}}