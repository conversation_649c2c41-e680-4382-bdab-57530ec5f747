import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import fs from 'fs/promises';
import path from 'path';
import open from 'open';
import { createServer } from 'http';
import { URL } from 'url';
import chalk from 'chalk';
import { AuthTokens, GmailConfig } from '../gmail/types';

export class GmailAuth {
  private oauth2Client: OAuth2Client;
  private config: GmailConfig;
  private tokenPath: string;

  constructor() {
    this.config = {
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
      redirectUri: process.env.GOOGLE_REDIRECT_URI || 'http://localhost:3000/oauth/callback',
      scopes: [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.modify'
      ]
    };

    this.tokenPath = process.env.TOKEN_STORAGE_PATH || './tokens.json';
    
    this.oauth2Client = new google.auth.OAuth2(
      this.config.clientId,
      this.config.clientSecret,
      this.config.redirectUri
    );
  }

  async authenticate(): Promise<OAuth2Client> {
    try {
      // Try to load existing tokens
      const tokens = await this.loadTokens();
      if (tokens) {
        this.oauth2Client.setCredentials(tokens);
        
        // Check if token is still valid
        if (await this.isTokenValid()) {
          console.log(chalk.green('✓ Using existing authentication'));
          return this.oauth2Client;
        }
      }
    } catch (error) {
      console.log(chalk.yellow('No valid tokens found, starting authentication flow...'));
    }

    // Start new authentication flow
    return this.startAuthFlow();
  }

  private async startAuthFlow(): Promise<OAuth2Client> {
    const authUrl = this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: this.config.scopes,
      prompt: 'consent'
    });

    console.log(chalk.blue('Opening browser for authentication...'));
    console.log(chalk.gray(`If browser doesn't open, visit: ${authUrl}`));
    
    await open(authUrl);

    const code = await this.waitForAuthCode();
    const { tokens } = await this.oauth2Client.getToken(code);
    
    this.oauth2Client.setCredentials(tokens);
    await this.saveTokens(tokens as AuthTokens);
    
    console.log(chalk.green('✓ Authentication successful!'));
    return this.oauth2Client;
  }

  private async waitForAuthCode(): Promise<string> {
    return new Promise((resolve, reject) => {
      const server = createServer((req, res) => {
        const url = new URL(req.url || '', `http://localhost:3000`);
        
        if (url.pathname === '/oauth/callback') {
          const code = url.searchParams.get('code');
          const error = url.searchParams.get('error');

          if (error) {
            res.writeHead(400, { 'Content-Type': 'text/html' });
            res.end('<h1>Authentication Failed</h1><p>You can close this window.</p>');
            server.close();
            reject(new Error(`Authentication failed: ${error}`));
            return;
          }

          if (code) {
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end('<h1>Authentication Successful</h1><p>You can close this window and return to the terminal.</p>');
            server.close();
            resolve(code);
            return;
          }
        }

        res.writeHead(404, { 'Content-Type': 'text/html' });
        res.end('<h1>Not Found</h1>');
      });

      const port = parseInt(process.env.OAUTH_PORT || '3000');
      server.listen(port, () => {
        console.log(chalk.gray(`Waiting for authentication on port ${port}...`));
      });

      server.on('error', (err) => {
        reject(new Error(`Server error: ${err.message}`));
      });
    });
  }

  private async loadTokens(): Promise<AuthTokens | null> {
    try {
      const tokenData = await fs.readFile(this.tokenPath, 'utf8');
      return JSON.parse(tokenData);
    } catch (error) {
      return null;
    }
  }

  private async saveTokens(tokens: AuthTokens): Promise<void> {
    const tokenDir = path.dirname(this.tokenPath);
    await fs.mkdir(tokenDir, { recursive: true });
    await fs.writeFile(this.tokenPath, JSON.stringify(tokens, null, 2));
  }

  private async isTokenValid(): Promise<boolean> {
    try {
      await this.oauth2Client.getAccessToken();
      return true;
    } catch (error) {
      return false;
    }
  }

  async revokeTokens(): Promise<void> {
    try {
      await this.oauth2Client.revokeCredentials();
      await fs.unlink(this.tokenPath);
      console.log(chalk.green('✓ Authentication revoked successfully'));
    } catch (error) {
      console.error(chalk.red('Error revoking tokens:'), error);
    }
  }

  getAuthenticatedClient(): OAuth2Client {
    return this.oauth2Client;
  }
}
