import { Command } from 'commander';
import inquirer from 'inquirer';
import chalk from 'chalk';
import { GmailAuth } from '../auth/oauth';
import { GmailClient } from '../gmail/client';
import { OutputFormatter } from './output';
import { FilterOptions, OutputOptions } from '../gmail/types';

// Auth command
export const authCommand = new Command('auth')
  .description('Authenticate with Gmail')
  .option('-r, --revoke', 'Revoke existing authentication')
  .action(async (options) => {
    const auth = new GmailAuth();
    
    try {
      if (options.revoke) {
        await auth.revokeTokens();
        return;
      }

      console.log(chalk.blue('Starting Gmail authentication...'));
      await auth.authenticate();
      console.log(chalk.green('✓ Authentication completed successfully!'));
      
    } catch (error) {
      console.error(chalk.red('Authentication failed:'), error);
      process.exit(1);
    }
  });

// List command
export const listCommand = new Command('list')
  .description('List Gmail messages')
  .option('-f, --from <email>', 'Filter by sender email')
  .option('-t, --to <email>', 'Filter by recipient email')
  .option('-s, --subject <text>', 'Filter by subject')
  .option('--after <date>', 'Show messages after date (YYYY/MM/DD)')
  .option('--before <date>', 'Show messages before date (YYYY/MM/DD)')
  .option('--unread', 'Show only unread messages')
  .option('--has-attachment', 'Show only messages with attachments')
  .option('-l, --label <label>', 'Filter by label')
  .option('--format <format>', 'Output format (table, json, csv)', 'table')
  .option('--fields <fields>', 'Comma-separated list of fields to include')
  .option('-n, --max-results <number>', 'Maximum number of results', '10')
  .option('--page-token <token>', 'Page token for pagination')
  .action(async (options) => {
    try {
      const auth = new GmailAuth();
      const authClient = await auth.authenticate();
      const gmailClient = new GmailClient(authClient);
      const formatter = new OutputFormatter(gmailClient);

      // Build filter options
      const filters: FilterOptions = {
        from: options.from,
        to: options.to,
        subject: options.subject,
        after: options.after,
        before: options.before,
        isUnread: options.unread,
        hasAttachment: options.hasAttachment,
        label: options.label
      };

      // Build search query
      const searchQuery = gmailClient.buildSearchQuery(filters);

      // Fetch messages
      console.log(chalk.blue('Fetching messages...'));
      const response = await gmailClient.listMessages({
        q: searchQuery || undefined,
        maxResults: parseInt(options.maxResults),
        pageToken: options.pageToken
      });

      // Format output
      const outputOptions: OutputOptions = {
        format: options.format,
        fields: options.fields ? options.fields.split(',').map((f: string) => f.trim()) : undefined,
        maxResults: parseInt(options.maxResults)
      };

      await formatter.formatMessages(response.messages, outputOptions);

      // Show pagination info
      if (response.nextPageToken) {
        console.log(chalk.gray(`\nNext page token: ${response.nextPageToken}`));
        console.log(chalk.gray(`Use --page-token ${response.nextPageToken} to get next page`));
      }

    } catch (error) {
      console.error(chalk.red('Failed to list messages:'), error);
      process.exit(1);
    }
  });

// Search command
export const searchCommand = new Command('search')
  .description('Search Gmail messages using Gmail search syntax')
  .argument('<query>', 'Gmail search query')
  .option('--format <format>', 'Output format (table, json, csv)', 'table')
  .option('--fields <fields>', 'Comma-separated list of fields to include')
  .option('-n, --max-results <number>', 'Maximum number of results', '10')
  .option('--page-token <token>', 'Page token for pagination')
  .action(async (query, options) => {
    try {
      const auth = new GmailAuth();
      const authClient = await auth.authenticate();
      const gmailClient = new GmailClient(authClient);
      const formatter = new OutputFormatter(gmailClient);

      console.log(chalk.blue(`Searching for: "${query}"`));
      
      const response = await gmailClient.searchMessages(
        query,
        parseInt(options.maxResults)
      );

      const outputOptions: OutputOptions = {
        format: options.format,
        fields: options.fields ? options.fields.split(',').map((f: string) => f.trim()) : undefined,
        maxResults: parseInt(options.maxResults)
      };

      await formatter.formatMessages(response.messages, outputOptions);

      if (response.nextPageToken) {
        console.log(chalk.gray(`\nNext page token: ${response.nextPageToken}`));
      }

    } catch (error) {
      console.error(chalk.red('Search failed:'), error);
      process.exit(1);
    }
  });

// Interactive command for guided usage
export const interactiveCommand = new Command('interactive')
  .alias('i')
  .description('Interactive mode with guided prompts')
  .action(async () => {
    try {
      const auth = new GmailAuth();
      const authClient = await auth.authenticate();
      const gmailClient = new GmailClient(authClient);
      const formatter = new OutputFormatter(gmailClient);

      console.log(chalk.blue('Welcome to Gmail CLI Interactive Mode!'));

      const answers = await inquirer.prompt([
        {
          type: 'list',
          name: 'action',
          message: 'What would you like to do?',
          choices: [
            { name: 'List recent messages', value: 'list' },
            { name: 'Search messages', value: 'search' },
            { name: 'Filter messages', value: 'filter' }
          ]
        }
      ]);

      if (answers.action === 'search') {
        const searchAnswers = await inquirer.prompt([
          {
            type: 'input',
            name: 'query',
            message: 'Enter search query:',
            validate: (input) => input.trim().length > 0 || 'Please enter a search query'
          },
          {
            type: 'list',
            name: 'format',
            message: 'Output format:',
            choices: ['table', 'json', 'csv'],
            default: 'table'
          },
          {
            type: 'number',
            name: 'maxResults',
            message: 'Maximum results:',
            default: 10,
            validate: (input) => input > 0 && input <= 100 || 'Please enter a number between 1 and 100'
          }
        ]);

        const response = await gmailClient.searchMessages(
          searchAnswers.query,
          searchAnswers.maxResults
        );

        await formatter.formatMessages(response.messages, {
          format: searchAnswers.format,
          maxResults: searchAnswers.maxResults
        });

      } else if (answers.action === 'filter') {
        const filterAnswers = await inquirer.prompt([
          {
            type: 'input',
            name: 'from',
            message: 'Filter by sender (optional):'
          },
          {
            type: 'input',
            name: 'subject',
            message: 'Filter by subject (optional):'
          },
          {
            type: 'confirm',
            name: 'unread',
            message: 'Show only unread messages?',
            default: false
          },
          {
            type: 'list',
            name: 'format',
            message: 'Output format:',
            choices: ['table', 'json', 'csv'],
            default: 'table'
          }
        ]);

        const filters: FilterOptions = {
          from: filterAnswers.from || undefined,
          subject: filterAnswers.subject || undefined,
          isUnread: filterAnswers.unread
        };

        const searchQuery = gmailClient.buildSearchQuery(filters);
        const response = await gmailClient.listMessages({
          q: searchQuery || undefined,
          maxResults: 10
        });

        await formatter.formatMessages(response.messages, {
          format: filterAnswers.format,
          maxResults: 10
        });

      } else {
        // List recent messages
        const response = await gmailClient.listMessages({ maxResults: 10 });
        await formatter.formatMessages(response.messages, {
          format: 'table',
          maxResults: 10
        });
      }

    } catch (error) {
      console.error(chalk.red('Interactive mode failed:'), error);
      process.exit(1);
    }
  });
