import Table from 'cli-table3';
import chalk from 'chalk';
import { createObjectCsvWriter } from 'csv-writer';
import { EmailMessage, OutputOptions } from '../gmail/types';
import { GmailClient } from '../gmail/client';

export class OutputFormatter {
  private gmailClient: GmailClient;

  constructor(gmailClient: GmailClient) {
    this.gmailClient = gmailClient;
  }

  async formatMessages(messages: EmailMessage[], options: OutputOptions): Promise<void> {
    if (messages.length === 0) {
      console.log(chalk.yellow('No messages found.'));
      return;
    }

    const extractedData = messages.map(msg => this.gmailClient.extractEmailData(msg));

    switch (options.format) {
      case 'table':
        this.formatAsTable(extractedData, options);
        break;
      case 'json':
        this.formatAsJson(extractedData, options);
        break;
      case 'csv':
        await this.formatAsCsv(extractedData, options);
        break;
      default:
        throw new Error(`Unsupported format: ${options.format}`);
    }
  }

  private formatAsTable(data: any[], options: OutputOptions): void {
    const table = new Table({
      head: [
        chalk.cyan('From'),
        chalk.cyan('Subject'),
        chalk.cyan('Date'),
        chalk.cyan('Snippet')
      ],
      colWidths: [30, 40, 20, 50],
      wordWrap: true
    });

    data.forEach(email => {
      const date = new Date(email.date || email.internalDate).toLocaleDateString();
      const snippet = this.truncateText(email.snippet, 45);
      const from = this.truncateText(this.extractEmailAddress(email.from), 25);
      const subject = this.truncateText(email.subject, 35);

      table.push([
        from,
        subject,
        date,
        snippet
      ]);
    });

    console.log(table.toString());
    console.log(chalk.gray(`\nShowing ${data.length} messages`));
  }

  private formatAsJson(data: any[], options: OutputOptions): void {
    const output = {
      count: data.length,
      messages: options.fields ? 
        data.map(msg => this.filterFields(msg, options.fields)) : 
        data
    };

    console.log(JSON.stringify(output, null, 2));
  }

  private async formatAsCsv(data: any[], options: OutputOptions): Promise<void> {
    const filename = `gmail-export-${new Date().toISOString().split('T')[0]}.csv`;
    
    const fields = options.fields || [
      'id', 'from', 'to', 'subject', 'date', 'snippet', 'labels'
    ];

    const csvWriter = createObjectCsvWriter({
      path: filename,
      header: fields.map(field => ({ id: field, title: field.toUpperCase() }))
    });

    const csvData = data.map(msg => {
      const filtered = this.filterFields(msg, fields);
      // Convert arrays to comma-separated strings for CSV
      if (filtered.labels && Array.isArray(filtered.labels)) {
        filtered.labels = filtered.labels.join(', ');
      }
      return filtered;
    });

    await csvWriter.writeRecords(csvData);
    console.log(chalk.green(`✓ Exported ${data.length} messages to ${filename}`));
  }

  private filterFields(obj: any, fields: string[]): any {
    const filtered: any = {};
    fields.forEach(field => {
      if (obj.hasOwnProperty(field)) {
        filtered[field] = obj[field];
      }
    });
    return filtered;
  }

  private truncateText(text: string, maxLength: number): string {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  }

  private extractEmailAddress(emailString: string): string {
    if (!emailString) return '';
    
    // Extract email from "Name <<EMAIL>>" format
    const match = emailString.match(/<([^>]+)>/);
    if (match) {
      return match[1];
    }
    
    // Return as-is if no angle brackets found
    return emailString;
  }

  formatError(error: Error): void {
    console.error(chalk.red('✗ Error:'), error.message);
    
    if (process.env.NODE_ENV === 'development') {
      console.error(chalk.gray(error.stack));
    }
  }

  formatSuccess(message: string): void {
    console.log(chalk.green('✓'), message);
  }

  formatWarning(message: string): void {
    console.log(chalk.yellow('⚠'), message);
  }

  formatInfo(message: string): void {
    console.log(chalk.blue('ℹ'), message);
  }

  showProgress(current: number, total: number, message: string = 'Processing'): void {
    const percentage = Math.round((current / total) * 100);
    const progressBar = '█'.repeat(Math.floor(percentage / 5)) + 
                       '░'.repeat(20 - Math.floor(percentage / 5));
    
    process.stdout.write(`\r${message}: [${progressBar}] ${percentage}% (${current}/${total})`);
    
    if (current === total) {
      process.stdout.write('\n');
    }
  }
}
