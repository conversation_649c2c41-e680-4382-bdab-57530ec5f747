import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import { EmailMessage, EmailListOptions, EmailListResponse, FilterOptions } from './types';

export class GmailClient {
  private gmail: any;

  constructor(auth: OAuth2Client) {
    this.gmail = google.gmail({ version: 'v1', auth });
  }

  async listMessages(options: EmailListOptions = {}): Promise<EmailListResponse> {
    try {
      const response = await this.gmail.users.messages.list({
        userId: 'me',
        maxResults: options.maxResults || 10,
        pageToken: options.pageToken,
        q: options.q,
        labelIds: options.labelIds,
        includeSpamTrash: options.includeSpamTrash || false
      });

      const messages = response.data.messages || [];
      const detailedMessages: EmailMessage[] = [];

      // Fetch detailed information for each message
      for (const message of messages) {
        const detailed = await this.getMessage(message.id);
        if (detailed) {
          detailedMessages.push(detailed);
        }
      }

      return {
        messages: detailedMessages,
        nextPageToken: response.data.nextPageToken,
        resultSizeEstimate: response.data.resultSizeEstimate || 0
      };
    } catch (error) {
      throw new Error(`Failed to list messages: ${error}`);
    }
  }

  async getMessage(messageId: string): Promise<EmailMessage | null> {
    try {
      const response = await this.gmail.users.messages.get({
        userId: 'me',
        id: messageId,
        format: 'full'
      });

      return response.data;
    } catch (error) {
      console.error(`Failed to get message ${messageId}:`, error);
      return null;
    }
  }

  async searchMessages(query: string, maxResults: number = 10): Promise<EmailListResponse> {
    return this.listMessages({
      q: query,
      maxResults
    });
  }

  buildSearchQuery(filters: FilterOptions): string {
    const queryParts: string[] = [];

    if (filters.from) {
      queryParts.push(`from:${filters.from}`);
    }

    if (filters.to) {
      queryParts.push(`to:${filters.to}`);
    }

    if (filters.subject) {
      queryParts.push(`subject:"${filters.subject}"`);
    }

    if (filters.after) {
      queryParts.push(`after:${filters.after}`);
    }

    if (filters.before) {
      queryParts.push(`before:${filters.before}`);
    }

    if (filters.hasAttachment) {
      queryParts.push('has:attachment');
    }

    if (filters.isUnread) {
      queryParts.push('is:unread');
    }

    if (filters.label) {
      queryParts.push(`label:${filters.label}`);
    }

    return queryParts.join(' ');
  }

  extractEmailData(message: EmailMessage) {
    const headers = message.payload.headers || [];
    
    const getHeader = (name: string): string => {
      const header = headers.find(h => h.name.toLowerCase() === name.toLowerCase());
      return header?.value || '';
    };

    return {
      id: message.id,
      threadId: message.threadId,
      from: getHeader('From'),
      to: getHeader('To'),
      subject: getHeader('Subject'),
      date: getHeader('Date'),
      snippet: message.snippet,
      labels: message.labelIds || [],
      size: message.sizeEstimate,
      internalDate: new Date(parseInt(message.internalDate)).toISOString()
    };
  }

  async getLabels(): Promise<any[]> {
    try {
      const response = await this.gmail.users.labels.list({
        userId: 'me'
      });

      return response.data.labels || [];
    } catch (error) {
      throw new Error(`Failed to get labels: ${error}`);
    }
  }

  async markAsRead(messageId: string): Promise<void> {
    try {
      await this.gmail.users.messages.modify({
        userId: 'me',
        id: messageId,
        requestBody: {
          removeLabelIds: ['UNREAD']
        }
      });
    } catch (error) {
      throw new Error(`Failed to mark message as read: ${error}`);
    }
  }

  async markAsUnread(messageId: string): Promise<void> {
    try {
      await this.gmail.users.messages.modify({
        userId: 'me',
        id: messageId,
        requestBody: {
          addLabelIds: ['UNREAD']
        }
      });
    } catch (error) {
      throw new Error(`Failed to mark message as unread: ${error}`);
    }
  }

  async addLabel(messageId: string, labelId: string): Promise<void> {
    try {
      await this.gmail.users.messages.modify({
        userId: 'me',
        id: messageId,
        requestBody: {
          addLabelIds: [labelId]
        }
      });
    } catch (error) {
      throw new Error(`Failed to add label: ${error}`);
    }
  }

  async removeLabel(messageId: string, labelId: string): Promise<void> {
    try {
      await this.gmail.users.messages.modify({
        userId: 'me',
        id: messageId,
        requestBody: {
          removeLabelIds: [labelId]
        }
      });
    } catch (error) {
      throw new Error(`Failed to remove label: ${error}`);
    }
  }
}
