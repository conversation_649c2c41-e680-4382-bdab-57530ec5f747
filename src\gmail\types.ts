export interface EmailMessage {
  id: string;
  threadId: string;
  snippet: string;
  payload: {
    headers: EmailHeader[];
    body?: {
      data?: string;
      size: number;
    };
    parts?: EmailPart[];
  };
  sizeEstimate: number;
  historyId: string;
  internalDate: string;
  labelIds: string[];
}

export interface EmailHeader {
  name: string;
  value: string;
}

export interface EmailPart {
  partId: string;
  mimeType: string;
  filename: string;
  headers: EmailHeader[];
  body: {
    data?: string;
    size: number;
  };
}

export interface EmailListOptions {
  maxResults?: number;
  pageToken?: string;
  q?: string; // Gmail search query
  labelIds?: string[];
  includeSpamTrash?: boolean;
}

export interface EmailListResponse {
  messages: EmailMessage[];
  nextPageToken?: string;
  resultSizeEstimate: number;
}

export interface FilterOptions {
  from?: string;
  to?: string;
  subject?: string;
  after?: string; // Date in YYYY/MM/DD format
  before?: string; // Date in YYYY/MM/DD format
  hasAttachment?: boolean;
  isUnread?: boolean;
  label?: string;
}

export interface OutputOptions {
  format: 'table' | 'json' | 'csv';
  fields?: string[];
  maxResults?: number;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  scope: string;
  token_type: string;
  expiry_date: number;
}

export interface GmailConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scopes: string[];
}
