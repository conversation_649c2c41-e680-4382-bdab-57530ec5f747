#!/usr/bin/env node

import { Command } from 'commander';
import dotenv from 'dotenv';
import { authCommand, listCommand, searchCommand, interactiveCommand } from './cli/commands';

// Load environment variables
dotenv.config();

const program = new Command();

program
  .name('gmail-cli')
  .description('A command-line interface for Gmail')
  .version('1.0.0');

// Add commands
program.addCommand(authCommand);
program.addCommand(listCommand);
program.addCommand(searchCommand);
program.addCommand(interactiveCommand);

// Parse command line arguments
program.parse();
